# Contributing Guidelines

Thank you for your interest in contributing to the f0ck.org project! This guide will help you understand how you can contribute effectively.

## Getting Started

### Setting Up the Development Environment

1. Fork the repository on GitHub
2. Clone your fork locally
3. Follow the [Installation Guide](Installation-Guide) to set up the project
4. Create a new branch for your contribution

### Development Workflow

1. Make your changes in your feature branch
2. Test your changes thoroughly
3. Commit your changes with clear, descriptive commit messages
4. Push your changes to your fork
5. Submit a pull request to the main repository

## Contribution Types

### Bug Fixes

If you've found a bug and want to fix it:

1. Check if there's an existing issue for the bug
2. If not, create a new issue describing the bug
3. Reference the issue in your pull request
4. Include steps to reproduce the bug
5. Explain your solution

### Feature Development

If you want to add a new feature:

1. Discuss the feature in an issue before implementing
2. Get approval from project maintainers
3. Implement the feature following project conventions
4. Include tests and documentation
5. Submit a pull request with a detailed description

### Documentation Improvements

Documentation contributions are highly valued:

1. Correct errors in existing documentation
2. Add examples and clarifications
3. Update documentation to reflect code changes
4. Create new documentation for undocumented features

## Code Style and Standards

### JavaScript/TypeScript

- Follow the ESLint configuration in the project
- Use TypeScript features appropriately
- Maintain consistent naming conventions
- Comment complex logic

### React/Next.js

- Use functional components with hooks
- Follow React best practices
- Use Server Components where appropriate
- Keep components focused and reusable

### CSS/TailwindCSS

- Use Tailwind utility classes for styling
- Follow responsive design principles
- Maintain consistent spacing and layout

## Pull Request Process

1. Ensure your code follows project conventions
2. Update documentation if necessary
3. Include tests for new functionality
4. Make sure all tests pass
5. Respond to review comments promptly
6. Be ready to make requested changes

## Commit Messages

Use clear, descriptive commit messages:

```
[type]: Short summary of changes (max 50 chars)

More detailed explanation if necessary. Wrap at 72 characters.
Explain what and why, not how (the code shows how).

Fixes #123
```

Where `[type]` is one of:
- `fix`: Bug fix
- `feat`: New feature
- `docs`: Documentation changes
- `style`: Formatting changes
- `refactor`: Code restructuring
- `test`: Testing changes
- `chore`: Maintenance tasks

## Code of Conduct

Please note that this project adheres to a Code of Conduct. By participating, you are expected to uphold this code.

- Be respectful and inclusive
- Value different viewpoints
- Accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

## Getting Help

If you need help with contributing:

- Join our [Discord server](https://discord.gg/SmWpwGnyrU)
- Check existing documentation
- Ask questions in issues or discussions
- Reach out to project maintainers

Thank you for contributing to the f0ck.org project! 