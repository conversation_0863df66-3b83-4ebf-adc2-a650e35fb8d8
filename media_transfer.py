import os
import shutil
import logging
from pathlib import Path
from config import MEDIA_PATH

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_target_dirs():
    Path(MEDIA_PATH['target']).mkdir(parents=True, exist_ok=True)

def transfer_media(post_data):
    """Überträgt Mediendateien von Szurubooru zu e621"""
    setup_target_dirs()
    
    for post in post_data:
        source_path = Path(MEDIA_PATH['source']) / post['md5']
        target_path = Path(MEDIA_PATH['target']) / post['md5']
        
        if source_path.exists():
            try:
                shutil.copy2(source_path, target_path)
                logger.info(f"Transferred: {post['md5']}")
            except Exception as e:
                logger.error(f"Failed to transfer {post['md5']}: {e}")
        else:
            logger.warning(f"Source file missing: {post['md5']}")

    return True
