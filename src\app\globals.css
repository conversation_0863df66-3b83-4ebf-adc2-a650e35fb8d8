@import "tailwindcss";
@plugin "@tailwindcss/forms";

:root {
  --background: #ffffff; /* <PERSON><PERSON> al<PERSON> Hintergrund */
  --foreground: #171717;
  --card-bg: #f9f9f9;
  --card-border: #e5e7eb;
  --text-primary: #171717;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --hover-bg: #f3f4f6;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card-bg: rgba(17, 24, 39, 0.5);
  --card-border: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  --text-tertiary: #9ca3af;
  --hover-bg: rgba(31, 41, 55, 0.5);
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@keyframes fade-in-out {
  0% { opacity: 0; transform: translate3d(0, -20px, 0); }
  15% { opacity: 1; transform: translate3d(0, 0, 0); }
  85% { opacity: 1; transform: translate3d(0, 0, 0); }
  100% { opacity: 0; transform: translate3d(0, 20px, 0); }
}

.animate-fade-in-out {
  animation: fade-in-out 2s ease-in-out forwards;
}

/* Neue Utility-Klassen für Nickname-Animationen */
.bg-size-200 {
  background-size: 200% 200%;
}

.animate-gpu {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}

@layer components {
  .stat-card {
    @apply p-5 rounded-xl 
           bg-[var(--card-bg)]
           backdrop-blur-xs
           border border-[var(--card-border)]
           text-center 
           transition-all duration-300
           hover:scale-[1.02] hover:shadow-xs
           hover:bg-gray-50/90 dark:hover:bg-gray-900/60;
  }
  
  /* Zurück zu den ursprünglichen Farbwerten */
  .stat-card > div:first-child {
    @apply text-2xl font-light tracking-tight;
  }

  .stat-card > div:last-child {
    @apply text-[13px] font-medium tracking-wide text-gray-500 dark:text-gray-400;
  }
  
  .logo-container {
    @apply transition-all duration-500
           hover:scale-[1.02];
  }

  .toggle-switch {
    @apply relative inline-block w-12 h-6 cursor-pointer; /* Kleinere Dimensionen */
  }

  .toggle-switch input[type="checkbox"] {
    @apply hidden;
  }

  .toggle-switch-background {
    @apply absolute inset-0 bg-gray-200 dark:bg-gray-700 
           rounded-full shadow-inner
           transition-colors duration-300;
  }

  .toggle-switch-handle {
    @apply absolute top-[2px] left-[2px] w-5 h-5
           bg-white dark:bg-gray-200
           rounded-full shadow-md
           transition-transform duration-300
           transform;
  }

  .toggle-switch input[type="checkbox"]:checked + .toggle-switch-background {
    @apply bg-[#05c46b] dark:bg-[#04b360];
  }

  .toggle-switch input[type="checkbox"]:checked + .toggle-switch-background .toggle-switch-handle {
    @apply translate-x-6;
  }

  .settings-card {
    @apply p-4 rounded-lg
           bg-[var(--card-bg)]
           backdrop-blur-xs
           border border-[var(--card-border)]
           transition-all duration-300
           space-y-3;
  }

  .settings-card h2 {
    @apply text-black dark:text-gray-400 font-semibold;
  }

  .settings-row {
    @apply flex items-center justify-between
           py-2
           border-b border-gray-100 dark:border-gray-800
           last:border-0
           text-sm;
  }
}

/* Styles für @-Erwähnungen */
.mention {
  color: #8b5cf6;
  font-weight: 500;
  background-color: rgba(139, 92, 246, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Styles für den Mention-Selector */
.mention-selector-container {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}
