MONGODB_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority
NEXTAUTH_SECRET=Generate Secret
NEXT_PUBLIC_GIPHY_API_KEY=Your giphy.com Key
NEXTAUTH_URL=http://localhost:3000
# NEXT_PUBLIC_API_URL=/api/upload <remove# in prod and rename to .env.production>
NEXTAUTH_COOKIE_DOMAIN=Your domain with https://
PORT=3001
NODE_ENV=development
# Email Configuration (SMTP)
# For Gmail, use smtp.gmail.com with port 587
# For other providers, check their SMTP settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
# Discord OAuth Configuration
AUTH_DISCORD_ID=Your Discord Application Client ID
AUTH_DISCORD_SECRET=Your Discord Application Client Secret