import json
import logging
import psycopg2
from media_transfer import transfer_media
from config import E621_DB, TAG_TYPE_MAP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def connect_db():
    return psycopg2.connect(**E621_DB)

def import_tags(tags):
    with connect_db() as conn:
        cur = conn.cursor()
        for tag in tags:
            cur.execute("""
                INSERT INTO tags (name, category)
                VALUES (%s, %s)
                ON CONFLICT (name) DO UPDATE
                SET category = EXCLUDED.category
            """, (tag['names'].split()[0],  # Hauptname
                  TAG_TYPE_MAP.get(tag['category'], 0)))

def import_posts(posts):
    with connect_db() as conn:
        cur = conn.cursor()
        for post in posts:
            cur.execute("""
                INSERT INTO posts (
                    md5, source, created_at, 
                    updated_at, rating
                ) VALUES (%s, %s, %s, %s, %s)
            """, (
                post['md5'],
                post['source'],
                post['created_at'],
                post['updated_at'],
                's' if post['safety'] == 'safe' else 'q'
            ))

def import_tag_relations(relations):
    with connect_db() as conn:
        cur = conn.cursor()
        for rel in relations:
            try:
                cur.execute("""
                    INSERT INTO tag_aliases (antecedent_name, consequent_name)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, (rel['tag1'].split()[0], rel['tag2'].split()[0]))
            except Exception as e:
                logger.error(f"Failed to import relation {rel}: {e}")

def import_users(users):
    with connect_db() as conn:
        cur = conn.cursor()
        for user in users:
            try:
                cur.execute("""
                    INSERT INTO users (name, password_hash, email, level)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (name) DO NOTHING
                """, (
                    user['name'],
                    user['password_hash'],
                    user['email'],
                    20 if user['rank'] == 'admin' else 10
                ))
            except Exception as e:
                logger.error(f"Failed to import user {user['name']}: {e}")

if __name__ == '__main__':
    try:
        with open('szuru_export.json') as f:
            data = json.load(f)
        
        import_tags(data['tags'])
        import_posts(data['posts'])
        import_tag_relations(data['tag_relations'])
        import_users(data['users'])
        
        # Medien übertragen
        transfer_media(data['posts'])
        
        logger.info("Import completed successfully")
        
    except Exception as e:
        logger.error(f"Import failed: {e}")
        raise
