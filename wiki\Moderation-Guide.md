# Moderation Guide

This guide provides information for moderators and administrators on how to effectively manage content and users on the f0ck.org platform.

## Moderation Interface

The moderation interface is accessible to users with moderator or administrator roles. It can be accessed at `/moderation` and provides several tabs for different moderation functions.

### Overview Tab

The Overview tab provides a dashboard with:

- Recent activity across the platform
- Reports requiring attention
- Statistics on content and user activity
- Quick access to common moderation actions

### Comments Tab

The Comments tab allows moderators to:

- View all comments across the platform
- Filter comments by status, author, or content
- Approve or reject pending comments
- Edit or delete inappropriate comments
- View comment reports

### Activity Log Tab

The Activity Log tab shows:

- All moderation actions taken by moderators
- User account changes
- Content status changes
- Filter options by action type, moderator, or date range

### Actions Tab

The Actions tab provides tools for:

- User management (ban, unban, role changes)
- Content management (feature, unfeature, remove)
- Tag management (create, edit, delete)
- System announcements

## Moderation Actions

### Content Moderation

#### Featuring Posts

1. Navigate to the post you want to feature
2. Click the "Feature" button in the post options
3. Confirm the action
4. The post will now appear in the featured section on the homepage

#### Unfeaturing Posts

1. Navigate to the featured post
2. Click the "Unfeature" button in the post options
3. Confirm the action
4. The post will be removed from the featured section

#### Pinning Posts

1. Navigate to the post you want to pin
2. Click the "Pin" button in the post options
3. Confirm the action
4. The post will now appear at the top of post listings

#### Removing Content

1. Navigate to the content you want to remove
2. Click the "Remove" button in the content options
3. Select a reason for removal from the dropdown
4. Add any additional notes (optional)
5. Confirm the action
6. The content will be hidden from public view

### User Moderation

#### Changing User Roles

1. Navigate to the user's profile
2. Click the "Manage User" button
3. Select "Change Role" from the dropdown
4. Choose the new role from the options:
   - User (standard permissions)
   - Premium (enhanced features)
   - Moderator (content moderation abilities)
   - Admin (full system access)
5. Confirm the action

#### Banning Users

1. Navigate to the user's profile
2. Click the "Manage User" button
3. Select "Ban User" from the dropdown
4. Choose a ban duration:
   - Temporary (1 day, 3 days, 7 days, 30 days)
   - Permanent
5. Enter a reason for the ban
6. Confirm the action

#### Unbanning Users

1. Navigate to the banned users list in the Actions tab
2. Find the user you want to unban
3. Click the "Unban" button
4. Confirm the action

### Tag Moderation

#### Managing Tags

1. Navigate to the Tags section in the Actions tab
2. View all tags with usage statistics
3. Options for each tag:
   - Edit: Change the tag name or merge with another tag
   - Delete: Remove the tag from the system
   - Blacklist: Prevent the tag from being used in future uploads

## Reporting System

### Handling Reports

1. Navigate to the Reports section in the Overview tab
2. Review the reported content and the reason for the report
3. Options for each report:
   - Dismiss: Mark the report as reviewed with no action
   - Take Action: Remove content, warn user, or ban user
   - Escalate: Flag for review by administrators

### Report Categories

- **Rule Violation**: Content that breaks platform rules
- **Illegal Content**: Content that may violate laws
- **Copyright Infringement**: Unauthorized use of copyrighted material
- **Harassment**: Content targeting specific users
- **Spam**: Unsolicited or repetitive content
- **Other**: Miscellaneous issues requiring review

## Moderation Best Practices

### General Guidelines

- Be consistent in applying rules
- Document actions taken in the moderation log
- Communicate clearly with users about rule violations
- Escalate complex situations to senior moderators or administrators
- Maintain user privacy and confidentiality

### Progressive Discipline

1. **Warning**: For first-time or minor violations
2. **Temporary Restrictions**: For repeated or moderate violations
3. **Extended Restrictions**: For serious or continued violations
4. **Permanent Ban**: For extreme violations or pattern of abuse

### Communication

- Use the built-in messaging system for user communications
- Maintain a professional tone
- Clearly explain the reason for moderation actions
- Provide guidance on how to comply with platform rules

## Moderation Tools

### Comment Management

- **Approval Queue**: Review comments before they appear publicly
- **Bulk Actions**: Apply actions to multiple comments at once
- **Keyword Filters**: Automatically flag comments containing specific terms

### Content Filters

- **Safe/Sketchy/Unsafe**: Manage content visibility based on rating
- **Tag Restrictions**: Control which tags require moderation
- **User Trust Levels**: Adjust moderation requirements based on user history

### User Management

- **User Notes**: Add private notes to user profiles for moderation purposes
- **Activity Monitoring**: View user activity patterns
- **IP Tracking**: Identify multiple accounts from the same source

## Escalation Procedures

### When to Escalate

- Legal concerns or threats
- Technical issues affecting moderation
- Disputes between users and moderators
- Pattern of problematic behavior requiring investigation

### How to Escalate

1. Document the situation thoroughly
2. Use the "Escalate" button in the moderation interface
3. Provide all relevant details and evidence
4. Tag appropriate administrators for review

## Regular Moderation Tasks

### Daily

- Review reported content
- Check comment approval queue
- Monitor featured content

### Weekly

- Review moderation logs for patterns
- Check for users approaching warning thresholds
- Update moderation notes

### Monthly

- Review moderation policies and suggest improvements
- Check tag usage and clean up unused or problematic tags
- Review user ban list for potential unbans
