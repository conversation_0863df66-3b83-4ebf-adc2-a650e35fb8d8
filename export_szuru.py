import json
import logging
import psycopg2
from config import SZURU_DB

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def connect_db():
    return psycopg2.connect(**SZURU_DB)

def export_posts():
    with connect_db() as conn:
        cur = conn.cursor()
        cur.execute("""
            SELECT id, creation_time, last_edit_time, 
                   safety, type, checksum, source
            FROM post
        """)
        posts = cur.fetchall()
        
    return [dict(zip(
        ['id', 'created_at', 'updated_at', 'safety', 
         'type', 'md5', 'source'], post)) 
        for post in posts]

def export_tags():
    with connect_db() as conn:
        cur = conn.cursor()
        cur.execute("SELECT id, names, category FROM tag")
        return [dict(zip(['id', 'names', 'category'], tag)) 
                for tag in cur.fetchall()]

def export_tag_relations():
    with connect_db() as conn:
        cur = conn.cursor()
        cur.execute("""
            SELECT
                t1.names as tag1_name,
                t2.names as tag2_name,
                tr.type
            FROM tag_relation tr
            JOIN tag t1 ON tr.first_tag_id = t1.id
            JOIN tag t2 ON tr.second_tag_id = t2.id
        """)
        return [dict(zip(['tag1', 'tag2', 'relation_type'], rel)) 
                for rel in cur.fetchall()]

def export_users():
    with connect_db() as conn:
        cur = conn.cursor()
        cur.execute("""
            SELECT name, password_hash, email, rank
            FROM user
            WHERE rank != 'anonymous'
        """)
        return [dict(zip(['name', 'password_hash', 'email', 'rank'], user))
                for user in cur.fetchall()]

if __name__ == '__main__':
    try:
        posts = export_posts()
        tags = export_tags()
        relations = export_tag_relations()
        users = export_users()
        
        export_data = {
            'posts': posts,
            'tags': tags,
            'tag_relations': relations,
            'users': users
        }
        
        with open('szuru_export.json', 'w') as f:
            json.dump(export_data, f, indent=2)
            
        logger.info("Export completed successfully")
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise
